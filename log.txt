createTestArchiveTask()
useArchiveProgress.ts:158 🧪 创建测试压缩任务
useArchiveProgress.ts:163 🧪 调用 addArchiveTask: test_archive_1753174625694, 测试压缩包.7z, 100
useGlobalProgress.ts:1119 📦 创建压缩进度任务: 测试压缩包.7z (100 个文件)，任务ID: archive-test_archive_1753174625694
useGlobalProgress.ts:1120 📦 任务添加后，tasks.value 长度: 1
useGlobalProgress.ts:1121 📦 任务添加后，activeTasks 长度: 1
useGlobalProgress.ts:1122 📦 当前所有 activeTasks: [{…}]0: fileName: "测试压缩包.7z"id: "archive-test_archive_1753174625694"progress: 0status: "in-progress"type: "archive"[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)
useArchiveProgress.ts:165 🧪 addArchiveTask 返回的任务ID: archive-test_archive_1753174625694
useGlobalProgress.ts:279 🔍 [hasActiveTasks] 计算过程: {progressTasksCount: 1, tusActiveTasksCount: 0, downloadActiveTasksCount: 0, result: true, progressTasksDetails: Array(1)}downloadActiveTasksCount: 0progressTasksCount: 1progressTasksDetails: Array(1)0: fileName: "测试压缩包.7z"id: "archive-test_archive_1753174625694"status: "in-progress"type: "archive"[[Prototype]]: Objectlength: 1[[Prototype]]: Array(0)result: truetusActiveTasksCount: 0[[Prototype]]: Object
useGlobalProgress.ts:1135 📦 [nextTick] 任务添加后状态检查: {tasksLength: 1, activeTasksLength: 1, hasActiveTasksValue: true, newTaskStatus: 'in-progress', newTaskType: 'archive', …}activeTasksLength: 1archiveTasksCount: 1hasActiveTasksValue: truenewTaskStatus: "in-progress"newTaskType: "archive"tasksLength: 1[[Prototype]]: Object
'test_archive_1753174625694'
useArchiveProgress.ts:169 🧪 === 第一次检查（100ms后）===
useStreamDownloadManager.ts:438 设置下载事件监听器
useStreamDownloadManager.ts:447 下载事件监听器设置完成
useStreamDownloadManager.ts:1341 🔄 等待 Electron 模块就绪...
useStreamDownloadManager.ts:478 🔄 检查下载模块是否已经就绪...
useGlobalProgress.ts:203 📚 历史记录初始化完成:
useGlobalProgress.ts:204   - 上传历史: 0 条
useGlobalProgress.ts:205   - 下载历史: 0 条
useGlobalProgress.ts:206   - 上传批量历史: 0 条
useGlobalProgress.ts:207   - 下载批量历史: 0 条
useGlobalProgress.ts:279 🔍 [hasActiveTasks] 计算过程: {progressTasksCount: 0, tusActiveTasksCount: 0, downloadActiveTasksCount: 0, result: false, progressTasksDetails: Array(0)}downloadActiveTasksCount: 0progressTasksCount: 0progressTasksDetails: Array(0)length: 0[[Prototype]]: Array(0)at: ƒ at()concat: ƒ concat()constructor: ƒ Array()copyWithin: ƒ copyWithin()entries: ƒ entries()every: ƒ every()fill: ƒ fill()filter: ƒ filter()find: ƒ find()findIndex: ƒ findIndex()findLast: ƒ findLast()findLastIndex: ƒ findLastIndex()flat: ƒ flat()flatMap: ƒ flatMap()forEach: ƒ forEach()includes: ƒ includes()indexOf: ƒ indexOf()join: ƒ join()keys: ƒ keys()lastIndexOf: ƒ lastIndexOf()length: 0map: ƒ map()pop: ƒ pop()push: ƒ push()reduce: ƒ reduce()reduceRight: ƒ reduceRight()reverse: ƒ reverse()shift: ƒ shift()slice: ƒ slice()some: ƒ some()sort: ƒ sort()splice: ƒ splice()toLocaleString: ƒ toLocaleString()toReversed: ƒ toReversed()toSorted: ƒ toSorted()toSpliced: ƒ toSpliced()toString: ƒ toString()unshift: ƒ unshift()values: ƒ values()with: ƒ with()Symbol(Symbol.iterator): ƒ values()Symbol(Symbol.unscopables): {at: true, copyWithin: true, entries: true, fill: true, find: true, …}[[Prototype]]: Objectresult: falsetusActiveTasksCount: 0[[Prototype]]: Object
useArchiveProgress.ts:172 🧪 基础状态: {hasActiveTasks: false, tasksLength: 0, activeTasksLength: 0}activeTasksLength: 0hasActiveTasks: falsetasksLength: 0[[Prototype]]: Object
useArchiveProgress.ts:178 🧪 所有任务详情: []
useArchiveProgress.ts:190 🧪 活跃任务详情: []
useArchiveProgress.ts:203 🧪 手动计算结果: {progressTasksCount: 0, shouldHaveActiveTasks: false, actualHasActiveTasks: false, mismatch: false}actualHasActiveTasks: falsemismatch: falseprogressTasksCount: 0shouldHaveActiveTasks: false[[Prototype]]: Object
useArchiveProgress.ts:216 🧪 全局进度指示器元素: null
useArchiveProgress.ts:219 🧪 任务项元素数量: 0
useStreamDownloadManager.ts:482 ✅ 下载模块已经就绪，无需等待
useStreamDownloadManager.ts:1344 📥 开始恢复已存在的下载任务...
useStreamDownloadManager.ts:525 从 Electron 端获取到 0 个任务
useStreamDownloadManager.ts:572 ✅ 任务恢复完成: 恢复 0 个未完成任务，跳过 0 个已完成任务
useStreamDownloadManager.ts:1349 ✅ 下载管理器初始化完成
useArchiveProgress.ts:224 🧪 === 第二次检查（500ms后）===
useStreamDownloadManager.ts:438 设置下载事件监听器
useStreamDownloadManager.ts:447 下载事件监听器设置完成
useStreamDownloadManager.ts:1341 🔄 等待 Electron 模块就绪...
useStreamDownloadManager.ts:478 🔄 检查下载模块是否已经就绪...
useGlobalProgress.ts:203 📚 历史记录初始化完成:
useGlobalProgress.ts:204   - 上传历史: 0 条
useGlobalProgress.ts:205   - 下载历史: 0 条
useGlobalProgress.ts:206   - 上传批量历史: 0 条
useGlobalProgress.ts:207   - 下载批量历史: 0 条
useGlobalProgress.ts:279 🔍 [hasActiveTasks] 计算过程: {progressTasksCount: 0, tusActiveTasksCount: 0, downloadActiveTasksCount: 0, result: false, progressTasksDetails: Array(0)}downloadActiveTasksCount: 0progressTasksCount: 0progressTasksDetails: []result: falsetusActiveTasksCount: 0[[Prototype]]: Object
useArchiveProgress.ts:226 🧪 延迟检查状态: {hasActiveTasks: false, tasksLength: 0, activeTasksLength: 0, archiveTasksCount: 0}activeTasksLength: 0archiveTasksCount: 0hasActiveTasks: falsetasksLength: 0[[Prototype]]: Object
useStreamDownloadManager.ts:482 ✅ 下载模块已经就绪，无需等待
useStreamDownloadManager.ts:1344 📥 开始恢复已存在的下载任务...
useStreamDownloadManager.ts:525 从 Electron 端获取到 0 个任务
useStreamDownloadManager.ts:572 ✅ 任务恢复完成: 恢复 0 个未完成任务，跳过 0 个已完成任务
useStreamDownloadManager.ts:1349 ✅ 下载管理器初始化完成
useArchiveProgress.ts:236 🧪 模拟进度更新: 50%
useGlobalProgress.ts:1183 📦 更新压缩进度: 测试压缩包.7z -> 50% (50/100) - test-file.txt
useArchiveProgress.ts:241 🧪 模拟进度更新: 100%
useGlobalProgress.ts:1183 📦 更新压缩进度: 测试压缩包.7z -> 100% (100/100) - final-file.txt
forceRefreshUI()
useArchiveProgress.ts:250 🧪 强制刷新UI状态
useStreamDownloadManager.ts:438 设置下载事件监听器
useStreamDownloadManager.ts:447 下载事件监听器设置完成
useStreamDownloadManager.ts:1341 🔄 等待 Electron 模块就绪...
useStreamDownloadManager.ts:478 🔄 检查下载模块是否已经就绪...
useGlobalProgress.ts:203 📚 历史记录初始化完成:
useGlobalProgress.ts:204   - 上传历史: 0 条
useGlobalProgress.ts:205   - 下载历史: 0 条
useGlobalProgress.ts:206   - 上传批量历史: 0 条
useGlobalProgress.ts:207   - 下载批量历史: 0 条
useGlobalProgress.ts:279 🔍 [hasActiveTasks] 计算过程: {progressTasksCount: 0, tusActiveTasksCount: 0, downloadActiveTasksCount: 0, result: false, progressTasksDetails: Array(0)}
useArchiveProgress.ts:257 🧪 强制刷新后状态: {hasActiveTasks: false, tasksLength: 0}
undefined
useStreamDownloadManager.ts:482 ✅ 下载模块已经就绪，无需等待
useStreamDownloadManager.ts:1344 📥 开始恢复已存在的下载任务...
useStreamDownloadManager.ts:525 从 Electron 端获取到 0 个任务
useStreamDownloadManager.ts:572 ✅ 任务恢复完成: 恢复 0 个未完成任务，跳过 0 个已完成任务
useStreamDownloadManager.ts:1349 ✅ 下载管理器初始化完成