import { onMounted, onUnmounted } from "vue";
import { useGlobalProgress } from "./useGlobalProgress";

/**
 * 压缩进度监听 Composable
 * 监听主进程的压缩任务事件，并同步到全局进度管理系统
 */
export function useArchiveProgress() {
  const { addArchiveTask, updateArchiveTaskProgress, completeArchiveTask, errorArchiveTask, activeTasks } = useGlobalProgress();

  let isListenersSetup = false;

  /**
   * 获取 Electron API
   */
  const getElectronAPI = () => (window as any).electronAPI;

  /**
   * 设置压缩事件监听器
   */
  const setupArchiveEventListeners = () => {
    if (isListenersSetup) {
      console.log("📦 压缩事件监听器已设置，跳过重复设置");
      return;
    }

    try {
      const api = getElectronAPI();

      if (!api || !api.archive) {
        console.warn("📦 Electron Archive API 不可用");
        return;
      }

      // 监听压缩任务创建事件
      api.archive.onTaskCreated((taskId: string, task: any) => {
        console.log(`📦 [渲染进程] 收到压缩任务创建事件: ${task.name} (ID: ${taskId})`, task);

        // 创建前端进度任务
        const fileName = task.name ? `${task.name}.7z` : `archive_${Date.now()}.7z`;
        const totalFiles = task.totalFiles || 0;

        console.log(`📦 [渲染进程] 准备创建前端进度任务: ${fileName}, 总文件数: ${totalFiles}`);
        const progressTaskId = addArchiveTask(taskId, fileName, totalFiles);
        console.log(`📦 [渲染进程] 前端进度任务创建完成，ID: ${progressTaskId}`);
      });

      // 监听压缩任务进度事件
      api.archive.onTaskProgress((taskId: string, progress: number, currentFile?: string) => {
        // 检查任务是否存在，如果不存在则创建
        const existingTask = activeTasks.value.find((t) => t.archiveTaskId === taskId);
        if (!existingTask) {
          console.log(`📦 [渲染进程] 任务不存在，自动创建: ${taskId}`);
          // 从任务ID推断文件名
          const fileName = taskId.includes("2G多UE工程文件") ? "2G多UE工程文件.7z" : `archive_${Date.now()}.7z`;
          addArchiveTask(taskId, fileName, 1603); // 使用已知的文件数量
        }

        // 更新前端进度
        updateArchiveTaskProgress(taskId, progress, currentFile);

        console.log(
          `📦 [渲染进程] 已调用 updateArchiveTaskProgress，当前任务列表:`,
          activeTasks.value
            .filter((t) => t.type === "archive")
            .map((t) => ({
              id: t.id,
              type: t.type,
              fileName: t.fileName,
              progress: t.progress,
              status: t.status,
              archiveTaskId: t.archiveTaskId,
            }))
        );
      });

      // 监听压缩任务完成事件
      api.archive.onTaskCompleted((taskId: string, result: any) => {
        console.log(`📦 压缩任务完成: ${taskId}`, result);

        // 完成前端任务
        completeArchiveTask(taskId, result?.archivePath);
      });

      // 监听压缩任务错误事件
      api.archive.onTaskError((taskId: string, error: string) => {
        console.error(`📦 压缩任务出错: ${taskId} - ${error}`);

        // 标记前端任务为错误状态
        errorArchiveTask(taskId, error);
      });

      // 监听压缩任务取消事件
      api.archive.onTaskCancelled((taskId: string) => {
        console.log(`📦 压缩任务取消: ${taskId}`);

        // 标记前端任务为取消状态
        errorArchiveTask(taskId, "用户取消");
      });

      isListenersSetup = true;
      console.log("📦 压缩事件监听器设置完成");
    } catch (error) {
      console.error("📦 设置压缩事件监听器失败:", error);
    }
  };

  /**
   * 清理事件监听器
   */
  const cleanupArchiveEventListeners = () => {
    if (!isListenersSetup) {
      return;
    }

    try {
      const api = getElectronAPI();

      if (api && api.archive && api.archive.removeAllListeners) {
        api.archive.removeAllListeners();
        console.log("📦 清理压缩事件监听器");
      }

      isListenersSetup = false;
    } catch (error) {
      console.error("📦 清理压缩事件监听器失败:", error);
    }
  };

  // 立即设置监听器（不等待组件挂载）
  setupArchiveEventListeners();

  // 添加全局调试函数
  if (typeof window !== "undefined") {
    (window as any).testArchiveEvents = () => {
      console.log("🧪 测试压缩事件监听器");
      const api = getElectronAPI();

      if (!api || !api.archive) {
        console.error("❌ Archive API 不可用");
        return false;
      }

      console.log("✅ Archive API 可用，设置测试监听器");
      console.log("📊 当前监听器设置状态:", isListenersSetup);
      console.log("📊 当前活跃任务:", activeTasks.value);

      // 设置临时测试监听器
      api.archive.onTaskProgress((taskId: string, progress: number, currentFile?: string) => {
        console.log(`🧪 [临时测试监听器] 收到进度事件: ${taskId} -> ${progress}% (${currentFile || ""})`);
      });

      return true;
    };

    // 🔧 增强的测试工具
    (window as any).createTestArchiveTask = () => {
      console.log("🧪 创建测试压缩任务");
      const testTaskId = `test_archive_${Date.now()}`;
      const testFileName = "测试压缩包.7z";
      const testTotalFiles = 100;

      console.log(`🧪 调用 addArchiveTask: ${testTaskId}, ${testFileName}, ${testTotalFiles}`);
      const progressTaskId = addArchiveTask(testTaskId, testFileName, testTotalFiles);
      console.log(`🧪 addArchiveTask 返回的任务ID: ${progressTaskId}`);

      // 立即检查任务是否被添加
      setTimeout(() => {
        console.log("🧪 === 第一次检查（100ms后）===");
        const { hasActiveTasks, tasks, activeTasks } = useGlobalProgress();

        console.log("🧪 基础状态:", {
          hasActiveTasks: hasActiveTasks.value,
          tasksLength: tasks.value.length,
          activeTasksLength: activeTasks.value.length,
        });

        console.log(
          "🧪 所有任务详情:",
          tasks.value.map((t) => ({
            id: t.id,
            type: t.type,
            fileName: t.fileName,
            status: t.status,
            progress: t.progress,
            archiveTaskId: t.archiveTaskId,
          }))
        );

        console.log(
          "🧪 活跃任务详情:",
          activeTasks.value.map((t) => ({
            id: t.id,
            type: t.type,
            fileName: t.fileName,
            status: t.status,
            progress: t.progress,
          }))
        );

        // 手动计算hasActiveTasks应该的值
        const progressTasks = tasks.value.filter((task) => ["pending", "in-progress", "paused"].includes(task.status));
        console.log("🧪 手动计算结果:", {
          progressTasksCount: progressTasks.length,
          shouldHaveActiveTasks: progressTasks.length > 0,
          actualHasActiveTasks: hasActiveTasks.value,
          mismatch: progressTasks.length > 0 !== hasActiveTasks.value,
        });

        // 检查DOM元素
        const progressIndicator =
          document.querySelector('[data-testid="global-progress-indicator"]') ||
          document.querySelector(".progress-indicator") ||
          document.querySelector(".progress-panel") ||
          document.querySelector('[class*="progress"]');
        console.log("🧪 全局进度指示器元素:", progressIndicator);

        const taskItems = document.querySelectorAll('[data-task-type="archive"]') || document.querySelectorAll(".task-item") || document.querySelectorAll('[class*="task"]');
        console.log("🧪 任务项元素数量:", taskItems.length);
      }, 100);

      // 第二次检查（延迟更长时间）
      setTimeout(() => {
        console.log("🧪 === 第二次检查（500ms后）===");
        const { hasActiveTasks, tasks, activeTasks } = useGlobalProgress();
        console.log("🧪 延迟检查状态:", {
          hasActiveTasks: hasActiveTasks.value,
          tasksLength: tasks.value.length,
          activeTasksLength: activeTasks.value.length,
          archiveTasksCount: tasks.value.filter((t) => t.type === "archive").length,
        });
      }, 500);

      // 模拟进度更新
      setTimeout(() => {
        console.log("🧪 模拟进度更新: 50%");
        updateArchiveTaskProgress(testTaskId, 50, "test-file.txt");
      }, 1000);

      setTimeout(() => {
        console.log("🧪 模拟进度更新: 100%");
        updateArchiveTaskProgress(testTaskId, 100, "final-file.txt");
      }, 2000);

      return testTaskId;
    };

    // 🔧 添加强制刷新UI的测试函数
    (window as any).forceRefreshUI = () => {
      console.log("🧪 强制刷新UI状态");
      const { tasks, hasActiveTasks } = useGlobalProgress();

      // 触发响应式更新
      const currentTasks = [...tasks.value];
      tasks.value.splice(0, tasks.value.length, ...currentTasks);

      console.log("🧪 强制刷新后状态:", {
        hasActiveTasks: hasActiveTasks.value,
        tasksLength: tasks.value.length,
      });
    };
  }

  // 组件挂载时确保监听器已设置
  onMounted(() => {
    if (!isListenersSetup) {
      console.log("📦 组件挂载时重新设置监听器");
      setupArchiveEventListeners();
    }
  });

  // 组件卸载时清理监听器
  onUnmounted(() => {
    cleanupArchiveEventListeners();
  });

  return {
    setupArchiveEventListeners,
    cleanupArchiveEventListeners,
    isListenersSetup: () => isListenersSetup,
  };
}
